import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, combineLatest } from 'rxjs';
import { takeUntil, take } from 'rxjs/operators';

import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { Country } from '../../../../../common/typings';

/**
 * Example component showing how to use EntityCountriesComponent for IP blocked countries
 * This demonstrates the proper setup and usage patterns
 */
@Component({
  selector: 'app-example-ip-blocked',
  templateUrl: './example-ip-blocked.component.html',
  styleUrls: ['./example-ip-blocked.component.scss']
})
export class ExampleIpBlockedComponent implements OnInit, OnDestroy {
  // IP blocked countries data
  ipBlockedCountries: string[] = [];
  ipBlockedCountriesInherited: string[] = [];
  
  // Restricted countries data (for comparison/additional functionality)
  restrictedCountries: string[] = [];
  restrictedCountriesInherited: string[] = [];
  
  // Common data
  entity: Entity;
  countries: Country[];
  canEdit: boolean = true;
  
  private destroyed$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>
  ) {
    // Get data from route resolver
    const { entity, countries } = this.route.snapshot.data;
    this.entity = entity;
    this.countries = countries;
  }

  ngOnInit(): void {
    if (!this.entity || !this.entity.entityParent) {
      return;
    }

    this.loadCountriesData();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  /**
   * Load both own and inherited country settings
   */
  private loadCountriesData(): void {
    // Initial load
    combineLatest([
      this.entitySettingsService.getSettings(this.entity.path, true),
      this.entitySettingsService.getSettings(this.entity.path)
    ]).pipe(
      take(1)
    ).subscribe(([ownSettings, inheritedSettings]) => {
      this.updateCountriesData(ownSettings, inheritedSettings);
    });

    // Subscribe to updates
    combineLatest([
      this.entitySettingsService.getSettings(this.entity.path, true),
      this.entitySettingsService.getSettings(this.entity.path)
    ]).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(([ownSettings, inheritedSettings]) => {
      this.updateCountriesData(ownSettings, inheritedSettings);
    });
  }

  /**
   * Update countries data from settings
   */
  private updateCountriesData(ownSettings: EntitySettingsModel, inheritedSettings: EntitySettingsModel): void {
    // IP blocked countries
    this.ipBlockedCountries = ownSettings?.merchantGameRestrictionsUseIpCountries || [];
    this.ipBlockedCountriesInherited = inheritedSettings?.merchantGameRestrictionsUseIpCountries || [];
    
    // Restricted countries (if needed)
    this.restrictedCountries = ownSettings?.restrictedCountries || [];
    this.restrictedCountriesInherited = inheritedSettings?.restrictedCountries || [];
  }

  /**
   * Handle IP blocked countries updates
   * This method is called by the EntityCountriesComponent when countries are updated
   */
  updateIpBlockedCountries(countries: string[]): void {
    this.ipBlockedCountries = countries;
  }

  /**
   * Handle restricted countries updates
   * This method is called by the EntityCountriesComponent when countries are updated
   */
  updateRestrictedCountries(countries: string[]): void {
    this.restrictedCountries = countries;
  }

  /**
   * Check if user can edit countries
   */
  get canEditCountries(): boolean {
    return this.canEdit && !!this.entity;
  }

  /**
   * Get the count of IP blocked countries (own + inherited)
   */
  get totalIpBlockedCountries(): number {
    const ownCount = this.ipBlockedCountries?.length || 0;
    const inheritedCount = this.ipBlockedCountriesInherited?.length || 0;
    return ownCount > 0 ? ownCount : inheritedCount;
  }

  /**
   * Check if IP blocked countries are inherited
   */
  get isIpBlockedInherited(): boolean {
    return (!this.ipBlockedCountries || this.ipBlockedCountries.length === 0) && 
           (this.ipBlockedCountriesInherited && this.ipBlockedCountriesInherited.length > 0);
  }

  /**
   * Get display text for IP blocked countries status
   */
  get ipBlockedStatusText(): string {
    if (this.totalIpBlockedCountries === 0) {
      return 'No IP blocked countries configured';
    }
    
    const count = this.totalIpBlockedCountries;
    const source = this.isIpBlockedInherited ? 'inherited' : 'own';
    return `${count} IP blocked ${count === 1 ? 'country' : 'countries'} (${source})`;
  }
}
