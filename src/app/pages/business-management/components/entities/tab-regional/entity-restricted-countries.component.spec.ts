import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';

import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { Country } from '../../../../../common/typings';
import { EntityCountriesComponent } from './entity-countries.component';
import { EntityRestrictedCountriesComponent } from './entity-restricted-countries.component';

describe('EntityRestrictedCountriesComponent', () => {
  let component: EntityRestrictedCountriesComponent;
  let fixture: ComponentFixture<EntityRestrictedCountriesComponent>;
  let mockEntitySettingsService: jasmine.SpyObj<EntitySettingsService<EntitySettingsModel>>;

  beforeEach(async () => {
    mockEntitySettingsService = jasmine.createSpyObj('EntitySettingsService', ['getSettings']);

    await TestBed.configureTestingModule({
      declarations: [EntityRestrictedCountriesComponent, EntityCountriesComponent],
      providers: [
        { provide: EntitySettingsService, useValue: mockEntitySettingsService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EntityRestrictedCountriesComponent);
    component = fixture.componentInstance;

    // Mock entity
    component.entity = {
      path: 'test:entity',
      countries: ['US', 'CA'],
      defaultCountry: 'US'
    } as Entity;

    // Mock countries
    component.countries = [
      { code: 'US', displayName: 'United States' },
      { code: 'CA', displayName: 'Canada' },
      { code: 'GB', displayName: 'United Kingdom' }
    ] as Country[];

    component.canEdit = true;
    component.disableDeleteCountries = false;
    component.accessToManageCountries = jasmine.createSpy('accessToManageCountries').and.returnValue(false);
    
    // Mock entity settings service
    mockEntitySettingsService.getSettings.and.callFake((path: string, ownOnly?: boolean) => {
      if (ownOnly) {
        return of({ restrictedCountries: ['GB'] } as EntitySettingsModel);
      } else {
        return of({ restrictedCountries: ['FR'] } as EntitySettingsModel);
      }
    });
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load restricted countries on init', () => {
    fixture.detectChanges();
    expect(mockEntitySettingsService.getSettings).toHaveBeenCalledWith('test:entity', true);
    expect(mockEntitySettingsService.getSettings).toHaveBeenCalledWith('test:entity');
    expect(component.restrictedCountries).toEqual(['GB']);
    expect(component.restrictedCountriesInherited).toEqual(['FR']);
  });

  it('should update restrictedCountries when onRestrictedCountriesChange is called', () => {
    const newCountries = ['US', 'CA'];
    component.onRestrictedCountriesChange(newCountries);
    expect(component.restrictedCountries).toEqual(newCountries);
  });

  it('should handle empty entity settings', () => {
    mockEntitySettingsService.getSettings.and.returnValue(of({} as EntitySettingsModel));
    fixture.detectChanges();
    expect(component.restrictedCountries).toEqual([]);
    expect(component.restrictedCountriesInherited).toEqual([]);
  });

  it('should not load data if entity is not provided', () => {
    component.entity = null;
    fixture.detectChanges();
    expect(mockEntitySettingsService.getSettings).not.toHaveBeenCalled();
  });
});
