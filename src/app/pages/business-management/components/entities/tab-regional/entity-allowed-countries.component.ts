import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { Entity } from '../../../../../common/models/entity.model';
import { Country } from '../../../../../common/typings';

@Component({
  selector: 'entity-allowed-countries',
  templateUrl: './entity-allowed-countries.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityAllowedCountriesComponent {
  @Input() entity: Entity;
  @Input() countries: Country[];
  @Input() disableDeleteCountries: boolean = false;
}
