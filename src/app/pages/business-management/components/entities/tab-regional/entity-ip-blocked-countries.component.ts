import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, finalize, switchMap, take, takeUntil } from 'rxjs/operators';

import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { codeArrayToObjectReducer, pushDefaultToStart } from '../../../../../common/services/entity.service';
import { Country } from '../../../../../common/typings';
import { MatCountryDialogComponent } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';
import { RemoveConfirmDialogComponent } from '../dialogs/remove-confirm-dialog.component';

interface EntityCountry {
  code: string;
  displayName: string;
  isDefault: boolean;
  isInherited: boolean;
}

@Component({
  selector: 'entity-ip-blocked-countries',
  templateUrl: './entity-ip-blocked-countries.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityIpBlockedCountriesComponent implements OnInit, OnDestroy {
  @Input() entity: Entity;
  @Input() countries: Country[];
  @Input() canEdit: boolean = true;
  @Input() ipBlockedCountries: string[] = [];
  @Input() ipBlockedCountriesInherited: string[] = [];

  @Output() ipBlockedCountriesChange = new EventEmitter<string[]>();

  searchControl: FormControl = new FormControl();
  dataSource: MatTableDataSource<EntityCountry>;
  rowActions: RowAction[];

  private readonly destroyed$ = new Subject<void>();
  private countriesHash: Object;

  get displayedColumns(): string[] {
    return ['displayName', 'status', ...(this.canEdit ? ['code'] : [])];
  }

  constructor(
    private dialog: MatDialog,
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.countriesHash = this.countries.reduce(codeArrayToObjectReducer, {});
    this.setRowActions();
    this.refreshData();

    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((filterValue: string) => {
      this.dataSource.filter = filterValue?.trim().toLowerCase();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  showCountryModal(event: Event): void {
    this.searchControl.reset();
    event.preventDefault();

    const dialogRef = this.dialog.open(MatCountryDialogComponent, {
      disableClose: true,
      width: '500px',
      data: {
        countriesType: 'ip-blocked',
        entity: this.entity,
        countries: this.countries,
        restrictedCountries: this.getCountriesForModal(),
        allAvailableCountries: this.countries.map(country => country.code),
        buildHash: true
      }
    });

    dialogRef.afterClosed().pipe(
      filter((data: { entity: Entity, selected: string[] }) => !!data && typeof data.entity !== 'undefined'),
      switchMap(({ entity, selected }) => {
        const finalCountries = selected.length ? selected : null;
        return this.entitySettingsService.patchSettings({
          merchantGameRestrictionsUseIpCountries: finalCountries
        }, entity.path);
      }),
      take(1),
      finalize(() => this.refreshData())
    ).subscribe(() => {
      this.notifications.success(
        this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountriesAdded')
      );
    });
  }

  removeCountryModal(countryCode: string): void {
    const removeDialog = this.dialog.open(RemoveConfirmDialogComponent, {
      width: '500px',
      data: { removeCode: countryCode },
      disableClose: true
    });

    removeDialog.afterClosed().pipe(
      filter(code => !!code && code in this.countriesHash),
      switchMap((code: string) => {
        const updatedCountries = this.ipBlockedCountries.filter(country => country !== code);
        const finalCountries = updatedCountries.length ? updatedCountries : null;
        return this.entitySettingsService.patchSettings({
          merchantGameRestrictionsUseIpCountries: finalCountries
        }, this.entity.path);
      }),
      take(1),
      finalize(() => this.refreshData())
    ).subscribe(() => {
      this.notifications.success(
        this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountryRemoved')
      );
    });
  }

  getCountry(code: string): Country {
    return this.countriesHash[code];
  }

  showRowActions(country: EntityCountry): boolean {
    return this.canEdit && !country.isInherited;
  }

  private getCountriesForModal(): string[] {
    return this.ipBlockedCountries.length ? this.ipBlockedCountries : this.ipBlockedCountriesInherited;
  }

  private setRowActions(): void {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.actionDelete',
        icon: 'delete',
        inMenu: true,
        fn: ({ code }) => this.removeCountryModal(code),
        canActivateFn: () => this.canEdit
      })
    ];
  }

  private refreshData(): void {
    const countriesList = this.ipBlockedCountries.length ? this.ipBlockedCountries : this.ipBlockedCountriesInherited;
    const countries = pushDefaultToStart(countriesList, this.entity?.defaultCountry);

    const data: EntityCountry[] = countries.map((code) => ({
      code,
      displayName: this.getCountry(code) ? this.getCountry(code).displayName : '',
      isDefault: false,
      isInherited: !this.ipBlockedCountries.length && this.ipBlockedCountriesInherited.length > 0
    }));

    this.dataSource = new MatTableDataSource(data);
    this.ipBlockedCountriesChange.emit(this.ipBlockedCountries);
    this.cdr.detectChanges();
  }
}
