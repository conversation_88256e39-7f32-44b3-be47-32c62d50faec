import { ChangeDetectionStrategy, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';

import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { Country } from '../../../../../common/typings';

@Component({
  selector: 'entity-ip-blocked-countries',
  templateUrl: './entity-ip-blocked-countries.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityIpBlockedCountriesComponent implements OnInit, OnDestroy {
  @Input() entity: Entity;
  @Input() countries: Country[];
  @Input() canEdit: boolean = true;

  ipBlockedCountries: string[] = [];

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>
  ) {}

  ngOnInit(): void {
    this.loadIpBlockedCountries();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onIpBlockedCountriesChange(countries: string[]): void {
    this.ipBlockedCountries = countries;
  }

  private loadIpBlockedCountries(): void {
    if (!this.entity) {
      return;
    }

    this.entitySettingsService.getSettings(this.entity.path).pipe(
      take(1),
      takeUntil(this.destroyed$)
    ).subscribe((entitySettings) => {
      this.ipBlockedCountries = entitySettings?.merchantGameRestrictionsUseIpCountries || [];
    });
  }
}
