import { ComponentFixture, TestBed } from '@angular/core/testing';

import { Entity } from '../../../../../common/models/entity.model';
import { Country } from '../../../../../common/typings';
import { EntityAllowedCountriesComponent } from './entity-allowed-countries.component';
import { EntityCountriesComponent } from './entity-countries.component';

describe('EntityAllowedCountriesComponent', () => {
  let component: EntityAllowedCountriesComponent;
  let fixture: ComponentFixture<EntityAllowedCountriesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [EntityAllowedCountriesComponent, EntityCountriesComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(EntityAllowedCountriesComponent);
    component = fixture.componentInstance;

    // Mock entity
    component.entity = {
      path: 'test:entity',
      countries: ['US', 'CA'],
      defaultCountry: 'US'
    } as Entity;

    // Mock countries
    component.countries = [
      { code: 'US', displayName: 'United States' },
      { code: 'CA', displayName: 'Canada' },
      { code: 'GB', displayName: 'United Kingdom' }
    ] as Country[];

    component.disableDeleteCountries = false;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have correct input properties', () => {
    expect(component.entity).toBeDefined();
    expect(component.countries).toBeDefined();
    expect(component.disableDeleteCountries).toBe(false);
  });

  it('should handle disableDeleteCountries input', () => {
    component.disableDeleteCountries = true;
    expect(component.disableDeleteCountries).toBe(true);
  });
});
