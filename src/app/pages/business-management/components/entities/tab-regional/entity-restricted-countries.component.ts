import { ChangeDetectionStrategy, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';

import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { Country } from '../../../../../common/typings';

@Component({
  selector: 'entity-restricted-countries',
  templateUrl: './entity-restricted-countries.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityRestrictedCountriesComponent implements OnInit, OnDestroy {
  @Input() entity: Entity;
  @Input() countries: Country[];
  @Input() canEdit: boolean = true;
  @Input() disableDeleteCountries: boolean = false;
  @Input() accessToManageCountries: (condition: boolean) => boolean;
  
  restrictedCountries: string[] = [];
  restrictedCountriesInherited: string[] = [];
  
  private readonly destroyed$ = new Subject<void>();

  constructor(
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>
  ) {}

  ngOnInit(): void {
    this.loadRestrictedCountries();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onRestrictedCountriesChange(countries: string[]): void {
    this.restrictedCountries = countries;
  }

  private loadRestrictedCountries(): void {
    if (!this.entity) {
      return;
    }

    this.entitySettingsService.getSettings(this.entity.path, true).pipe(
      take(1),
      takeUntil(this.destroyed$)
    ).subscribe((ownEntitySettings) => {
      this.restrictedCountries = ownEntitySettings?.restrictedCountries || [];
    });

    this.entitySettingsService.getSettings(this.entity.path).pipe(
      take(1),
      takeUntil(this.destroyed$)
    ).subscribe((settingsInherited) => {
      this.restrictedCountriesInherited = settingsInherited?.restrictedCountries || [];
    });
  }
}
