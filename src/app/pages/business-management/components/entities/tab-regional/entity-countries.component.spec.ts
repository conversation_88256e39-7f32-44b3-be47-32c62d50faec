import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ChangeDetectorRef } from '@angular/core';
import { of } from 'rxjs';

import { EntityCountriesComponent } from './entity-countries.component';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { CountryService } from '../../../../../common/services/country.service';
import { EntityService } from '../../../../../common/services/entity.service';
import { Entity } from '../../../../../common/models/entity.model';
import { Country } from '../../../../../common/typings';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';

describe('EntityCountriesComponent', () => {
  let component: EntityCountriesComponent;
  let fixture: ComponentFixture<EntityCountriesComponent>;
  let mockEntitySettingsService: jasmine.SpyObj<EntitySettingsService<EntitySettingsModel>>;
  let mockCountryService: jasmine.SpyObj<CountryService>;
  let mockEntityService: jasmine.SpyObj<EntityService>;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockNotificationsService: jasmine.SpyObj<SwuiNotificationsService>;
  let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

  beforeEach(async () => {
    mockEntitySettingsService = jasmine.createSpyObj('EntitySettingsService', ['getSettings', 'patchSettings']);
    mockCountryService = jasmine.createSpyObj('CountryService', ['getList', 'deleteCountry']);
    mockEntityService = jasmine.createSpyObj('EntityService', ['updateEntityItem']);
    mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
    mockNotificationsService = jasmine.createSpyObj('SwuiNotificationsService', ['success']);
    mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);

    await TestBed.configureTestingModule({
      declarations: [EntityCountriesComponent],
      imports: [
        ReactiveFormsModule,
        MatTableModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        MatInputModule,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        { provide: EntitySettingsService, useValue: mockEntitySettingsService },
        { provide: CountryService, useValue: mockCountryService },
        { provide: EntityService, useValue: mockEntityService },
        { provide: MatDialog, useValue: mockDialog },
        { provide: SwuiNotificationsService, useValue: mockNotificationsService },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EntityCountriesComponent);
    component = fixture.componentInstance;

    // Mock entity
    component.entity = {
      path: 'test:entity',
      countries: ['US', 'CA'],
      defaultCountry: 'US'
    } as Entity;

    // Mock countries
    component.countries = [
      { code: 'US', displayName: 'United States' },
      { code: 'CA', displayName: 'Canada' },
      { code: 'GB', displayName: 'United Kingdom' }
    ] as Country[];

    // Mock country service
    mockCountryService.getList.and.returnValue(of([
      { code: 'US', displayName: 'United States' },
      { code: 'CA', displayName: 'Canada' },
      { code: 'GB', displayName: 'United Kingdom' }
    ] as Country[]));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('countriesType: allowed', () => {
    beforeEach(() => {
      component.countriesType = 'allowed';
      fixture.detectChanges();
    });

    it('should display allowed countries', () => {
      expect(component.countriesType).toBe('allowed');
      expect(component.displayedColumns).toEqual(['displayName', 'status', 'code']);
    });

    it('should show row actions when not disabled', () => {
      component.disabledList = false;
      const country = { code: 'US', displayName: 'United States', isDefault: false, isInherited: false };
      expect(component.showRowActions(country)).toBe(true);
    });
  });

  describe('countriesType: restricted', () => {
    beforeEach(() => {
      component.countriesType = 'restricted';
      component.restrictedCountries = ['GB'];
      component.restrictedCountriesInherited = ['FR'];
      fixture.detectChanges();
    });

    it('should display restricted countries', () => {
      expect(component.countriesType).toBe('restricted');
      expect(component.getCountriesForModal()).toEqual(['GB']);
    });

    it('should use inherited countries when no own countries', () => {
      component.restrictedCountries = [];
      expect(component.getCountriesForModal()).toEqual(['FR']);
    });
  });

  describe('countriesType: ip-blocked', () => {
    beforeEach(() => {
      component.countriesType = 'ip-blocked';
      component.ipBlockedCountries = ['CN'];
      component.ipBlockedCountriesInherited = ['RU'];

      // Mock settings for IP blocked countries
      const mockOwnSettings = {
        merchantGameRestrictionsUseIpCountries: ['CN']
      } as EntitySettingsModel;

      const mockInheritedSettings = {
        merchantGameRestrictionsUseIpCountries: ['RU']
      } as EntitySettingsModel;

      mockEntitySettingsService.getSettings.and.callFake((path: string, ownOnly?: boolean) => {
        return of(ownOnly ? mockOwnSettings : mockInheritedSettings);
      });

      fixture.detectChanges();
    });

    it('should display ip-blocked countries', () => {
      expect(component.countriesType).toBe('ip-blocked');
      expect(component.getCountriesForModal()).toEqual(['CN']);
    });

    it('should use inherited IP blocked countries when no own countries', () => {
      component.ipBlockedCountries = [];
      expect(component.getCountriesForModal()).toEqual(['RU']);
    });

    it('should load IP blocked countries on init', () => {
      component.ngOnInit();
      expect(mockEntitySettingsService.getSettings).toHaveBeenCalledWith('test:entity', true);
      expect(mockEntitySettingsService.getSettings).toHaveBeenCalledWith('test:entity');
    });

    it('should handle modal data correctly for IP blocked type', () => {
      const modalData = component['getCountriesForModal']();
      expect(modalData).toEqual(['CN']);
    });
  });

  describe('displayedColumns', () => {
    it('should include code column when not disabled', () => {
      component.disabledList = false;
      expect(component.displayedColumns).toEqual(['displayName', 'status', 'code']);
    });

    it('should exclude code column when disabled', () => {
      component.disabledList = true;
      expect(component.displayedColumns).toEqual(['displayName', 'status']);
    });
  });

  describe('showRowActions', () => {
    it('should return false when disabled', () => {
      component.disabledList = true;
      const country = { code: 'US', displayName: 'United States', isDefault: false, isInherited: false };
      expect(component.showRowActions(country)).toBe(false);
    });

    it('should return false when country is inherited', () => {
      component.disabledList = false;
      const country = { code: 'US', displayName: 'United States', isDefault: false, isInherited: true };
      expect(component.showRowActions(country)).toBe(false);
    });

    it('should return true when not disabled and not inherited', () => {
      component.disabledList = false;
      const country = { code: 'US', displayName: 'United States', isDefault: false, isInherited: false };
      expect(component.showRowActions(country)).toBe(true);
    });
  });
});
