import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridRowActionsModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { HintsModule } from '../../../../../common/components/hints/hints.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';

import { PipesModule } from '../../../../../common/pipes/pipes.module';
import { DeploymentGroupService } from '../../../../../common/services/deployment-group.service';
import { JurisdictionService } from '../../../../../common/services/jurisdiction.service';
import { CountriesResolver } from '../../../../../common/services/resolvers/countries.resolver';
import { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';
import { EntityBalancesResolver } from '../../../../../common/services/resolvers/entity-balances.resolver';
import { LanguagesResolver } from '../../../../../common/services/resolvers/languages.resolver';
import { ManageBalanceModule } from '../../mat-business-structure/dialogs/manage-balance/manage-balance.module';
import { MatCountryDialogModule } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.module';
import { MatCurrencyDialogModule } from '../../mat-business-structure/dialogs/mat-currency-dialog/mat-currency-dialog.module';
import {
  MatJurisdictionsDialogModule
} from '../../mat-business-structure/dialogs/mat-jurisdictions-dialog/mat-jurisdictions-dialog.module';
import { MatLanguageDialogModule } from '../../mat-business-structure/dialogs/mat-language-dialog/mat-language-dialog.module';
import { EntitySetupDialogsModule } from '../dialogs/entity-setup-dialogs.module';
import { SetupEntityDeploymentGroupsResolver } from '../resolvers/setup-entity-deployment-groups.resolver';
import { SetupEntityJurisdictionsResolver } from '../resolvers/setup-entity-jurisdictions.resolver';
import { EntityCountriesComponent } from './entity-countries.component';
import { EntityCurrenciesComponent } from './entity-currencies.component';
import { EntityDeploymentGroupsComponent } from './entity-deployment-groups.component';
import { EntityIpBlockedCountriesComponent } from './entity-ip-blocked-countries.component';
import { EntityJurisdictionsComponent } from './entity-jurisdictions.component';
import { EntityLanguagesComponent } from './entity-languages.component';
import { EntityRestrictedCountriesComponent } from './entity-restricted-countries.component';
import { TabRegionalComponent } from './tab-regional.component';
import { TabRegionalRoutingModule } from './tab-regional.routing';


@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        PipesModule,
        TabRegionalRoutingModule,
        MatTableModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        SwuiGridRowActionsModule,
        ManageBalanceModule,
        MatCountryDialogModule,
        MatLanguageDialogModule,
        MatCurrencyDialogModule,
        MatJurisdictionsDialogModule,
        EntitySetupDialogsModule,
        MatInputModule,
        ReactiveFormsModule,
        HintsModule,
        MatSlideToggleModule,
        SwuiSelectModule,
        MatSelectModule,
        TrimInputValueModule,
    ],
  declarations: [
    TabRegionalComponent,
    EntityCountriesComponent,
    EntityCurrenciesComponent,
    EntityLanguagesComponent,
    EntityJurisdictionsComponent,
    EntityDeploymentGroupsComponent,
    EntityIpBlockedCountriesComponent,
    EntityRestrictedCountriesComponent,
  ],
  exports: [
    TabRegionalComponent,
  ],
  providers: [
    JurisdictionService,
    LanguagesResolver,
    CountriesResolver,
    CurrenciesResolver,
    EntityBalancesResolver,
    SetupEntityJurisdictionsResolver,
    DeploymentGroupService,
    SetupEntityDeploymentGroupsResolver,
  ],
})
export class TabRegionalModule {
}
