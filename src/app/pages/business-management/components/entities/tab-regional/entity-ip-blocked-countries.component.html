<div class="tables-row__header">
  <div class="tables-row__title">{{ 'ENTITY_SETUP.REGIONAL.IP_BLOCKED_COUNTRIES.title' | translate }}</div>
  <button mat-icon-button
          (click)="showCountryModal($event)"
          matTooltip="{{ 'ENTITY_SETUP.REGIONAL.manage' | translate }}"
          *ngIf="canEdit">
    <mat-icon>tune</mat-icon>
  </button>
</div>

<mat-table [dataSource]="dataSource" class="mat-table--simple">
  <ng-container matColumnDef="displayName">
    <mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.REGIONAL.countryName' | translate }}</mat-header-cell>
    <mat-cell *matCellDef="let country">
      <span [ngClass]="{'text-bold': country['isDefault'] }">
        {{ country.displayName + ' (' + country.code + ')' }}
      </span>
    </mat-cell>
  </ng-container>

  <ng-container matColumnDef="status">
    <mat-header-cell *matHeaderCellDef></mat-header-cell>
    <mat-cell *matCellDef="let country" style="justify-content: flex-end">
      <span *ngIf="country['isDefault']" class="sw-chip"> {{ 'ENTITY_SETUP.REGIONAL.default' | translate }}</span>
    </mat-cell>
  </ng-container>

  <ng-container matColumnDef="code">
    <mat-header-cell *matHeaderCellDef class="mat-table align-right">
      <input matInput trimValue
             class="search-field"
             autocomplete="off"
             [formControl]="searchControl"
             placeholder="{{'ENTITY_SETUP.GAMES.searchPlaceholder' | translate}}">
    </mat-header-cell>
    <mat-cell *matCellDef="let country" class="align-right">
      <lib-swui-grid-row-actions [actions]="(showRowActions(country) ? rowActions : [])"
                                 [row]="country"
                                 [ignorePlainLink]="true"
                                 menuIcon="menu">
      </lib-swui-grid-row-actions>
      <span *ngIf="country['isInherited']" class="sw-chip"> Inherited </span>
    </mat-cell>
  </ng-container>

  <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>
  <mat-row *matRowDef="let country; columns: displayedColumns;"></mat-row>
</mat-table>
