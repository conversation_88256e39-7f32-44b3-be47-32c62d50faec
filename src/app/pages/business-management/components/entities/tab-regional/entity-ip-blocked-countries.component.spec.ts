import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of } from 'rxjs';

import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { Country } from '../../../../../common/typings';
import { EntityIpBlockedCountriesComponent } from './entity-ip-blocked-countries.component';

describe('EntityIpBlockedCountriesComponent', () => {
  let component: EntityIpBlockedCountriesComponent;
  let fixture: ComponentFixture<EntityIpBlockedCountriesComponent>;
  let mockEntitySettingsService: jasmine.SpyObj<EntitySettingsService<EntitySettingsModel>>;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockNotificationsService: jasmine.SpyObj<SwuiNotificationsService>;

  beforeEach(async () => {
    mockEntitySettingsService = jasmine.createSpyObj('EntitySettingsService', ['patchSettings']);
    mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
    mockNotificationsService = jasmine.createSpyObj('SwuiNotificationsService', ['success']);

    await TestBed.configureTestingModule({
      declarations: [EntityIpBlockedCountriesComponent],
      imports: [
        ReactiveFormsModule,
        MatTableModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        MatInputModule,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        { provide: EntitySettingsService, useValue: mockEntitySettingsService },
        { provide: MatDialog, useValue: mockDialog },
        { provide: SwuiNotificationsService, useValue: mockNotificationsService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EntityIpBlockedCountriesComponent);
    component = fixture.componentInstance;

    // Mock entity
    component.entity = {
      path: 'test:entity',
      defaultCountry: 'US'
    } as Entity;

    // Mock countries
    component.countries = [
      { code: 'US', displayName: 'United States' },
      { code: 'CA', displayName: 'Canada' },
      { code: 'CN', displayName: 'China' }
    ] as Country[];

    component.canEdit = true;

    // Mock entity settings service
    mockEntitySettingsService.getSettings.and.returnValue(
      of({ merchantGameRestrictionsUseIpCountries: ['CN'] } as EntitySettingsModel)
    );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display IP blocked countries', () => {
    fixture.detectChanges();
    expect(component.displayedColumns).toEqual(['displayName', 'status', 'code']);
  });

  it('should hide code column when canEdit is false', () => {
    component.canEdit = false;
    expect(component.displayedColumns).toEqual(['displayName', 'status']);
  });

  it('should show row actions when canEdit is true', () => {
    component.canEdit = true;
    expect(component.showRowActions()).toBe(true);
  });

  it('should not show row actions when canEdit is false', () => {
    component.canEdit = false;
    expect(component.showRowActions()).toBe(false);
  });

  it('should return own countries for modal', () => {
    component.ipBlockedCountries = ['CN'];
    expect(component['getCountriesForModal']()).toEqual(['CN']);
  });

  it('should return empty array for modal when no countries', () => {
    component.ipBlockedCountries = [];
    expect(component['getCountriesForModal']()).toEqual([]);
  });

  it('should load IP blocked countries on init', () => {
    fixture.detectChanges();
    expect(mockEntitySettingsService.getSettings).toHaveBeenCalledWith('test:entity', true);
  });

  it('should open country modal when showCountryModal is called', () => {
    const mockDialogRef = { afterClosed: () => of(null) };
    mockDialog.open.and.returnValue(mockDialogRef as any);

    const event = new Event('click');
    component.showCountryModal(event);

    expect(mockDialog.open).toHaveBeenCalled();
  });

  it('should open remove confirmation modal when removeCountryModal is called', () => {
    const mockDialogRef = { afterClosed: () => of(null) };
    mockDialog.open.and.returnValue(mockDialogRef as any);

    component.removeCountryModal('CN');

    expect(mockDialog.open).toHaveBeenCalled();
  });
});
