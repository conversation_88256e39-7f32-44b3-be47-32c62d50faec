import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';

import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { Country } from '../../../../../common/typings';
import { EntityCountriesComponent } from './entity-countries.component';
import { EntityIpBlockedCountriesComponent } from './entity-ip-blocked-countries.component';

describe('EntityIpBlockedCountriesComponent', () => {
  let component: EntityIpBlockedCountriesComponent;
  let fixture: ComponentFixture<EntityIpBlockedCountriesComponent>;
  let mockEntitySettingsService: jasmine.SpyObj<EntitySettingsService<EntitySettingsModel>>;

  beforeEach(async () => {
    mockEntitySettingsService = jasmine.createSpyObj('EntitySettingsService', ['getSettings']);

    await TestBed.configureTestingModule({
      declarations: [EntityIpBlockedCountriesComponent, EntityCountriesComponent],
      providers: [
        { provide: EntitySettingsService, useValue: mockEntitySettingsService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EntityIpBlockedCountriesComponent);
    component = fixture.componentInstance;

    // Mock entity
    component.entity = {
      path: 'test:entity',
      defaultCountry: 'US'
    } as Entity;

    // Mock countries
    component.countries = [
      { code: 'US', displayName: 'United States' },
      { code: 'CA', displayName: 'Canada' },
      { code: 'CN', displayName: 'China' }
    ] as Country[];

    component.canEdit = true;

    // Mock entity settings service
    mockEntitySettingsService.getSettings.and.returnValue(
      of({ merchantGameRestrictionsUseIpCountries: ['CN'] } as EntitySettingsModel)
    );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load IP blocked countries on init', () => {
    fixture.detectChanges();
    expect(mockEntitySettingsService.getSettings).toHaveBeenCalledWith('test:entity');
    expect(component.ipBlockedCountries).toEqual(['CN']);
  });

  it('should update ipBlockedCountries when onIpBlockedCountriesChange is called', () => {
    const newCountries = ['US', 'CA'];
    component.onIpBlockedCountriesChange(newCountries);
    expect(component.ipBlockedCountries).toEqual(newCountries);
  });

  it('should handle empty entity settings', () => {
    mockEntitySettingsService.getSettings.and.returnValue(of({} as EntitySettingsModel));
    fixture.detectChanges();
    expect(component.ipBlockedCountries).toEqual([]);
  });
});
