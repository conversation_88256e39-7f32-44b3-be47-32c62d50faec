import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Entity } from '../../../../../../common/models/entity.model';
import { codeArrayToObjectReducer } from '../../../../../../common/services/entity.service';

import { Country } from '../../../../../../common/typings';
import { StructureEntityModel } from '../../structure-entity.model';


export interface MatCountryDialogData {
  entity: StructureEntityModel;
  countries: Country[];
  restrictedCountries: string[];
  allAvailableCountries: string[];
  countriesType: 'allowed' | 'restricted' | 'ip-blocked';
  buildHash?: boolean;
}

export class CountryItem {
  code: string;
  displayName: string;
  selected?: boolean;
  disabled?: boolean;

  constructor( data: Country ) {
    this.code = data.code || '';
    this.displayName = data.displayName || '';
  }
}

@Component({
  selector: 'mat-country-dialog',
  templateUrl: './mat-country-dialog.component.html',
  styleUrls: ['./mat-country-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MatCountryDialogComponent implements OnInit {

  allAvailableCountries: string[] = [];
  entity: Entity;
  countries: Country[];
  defaultCountry: CountryItem;
  dataSource: MatTableDataSource<CountryItem>;
  restrictedCountries: string[] = [];
  countriesType: 'allowed' | 'restricted' | 'ip-blocked';

  nameFilter: string;
  displayedColumns: string[] = ['name', 'code'];
  allSelected = false;
  submitted = false;

  private countriesHash: { [code: string]: Country };

  get selectedItems(): CountryItem[] {
    return this.dataSource.data.filter(item => item.selected);
  }

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MatCountryDialogData,
    public dialogRef: MatDialogRef<MatCountryDialogComponent>,
  ) {
    this.setDialogData();
    this.setCountriesHash();
  }

  ngOnInit(): void {
    this.setAvailableCountries();
  }

  applyChanges() {
    this.dialogRef.close({
      entity: this.entity,
      selected: this.selectedItems.map(item => item.code)
    });
  }

  applyFilter( filterValue: string ) {
    this.nameFilter = filterValue;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.allSelected = this.dataSource.filteredData.every(item => item.selected);
  }

  allSelectedChanged( changeEvent: MatCheckboxChange ) {
    this.dataSource.data.map(item => item.selected = false);

    if (changeEvent.checked) {
      this.dataSource.filteredData.map(item => {
        item.selected = !(item.code === this.entity.defaultCountry && this.countriesType === 'restricted');
      });
    }
  }

  private setAvailableCountries() {
    let countries = this.countriesType === 'allowed' ? this.entity.entityParent.countries : this.allAvailableCountries;
    let rootParent = this.entity.entityParent.isRoot();
    let rootCountriesMismatch = countries.length !== this.countries.length;

    if (rootParent && rootCountriesMismatch) {
      countries = Object.keys(this.countriesHash);
    }

    const items = countries
      .map(( code ) => {
        const item = new CountryItem(this.countriesHash[code]);

        item.selected = this.countriesType === 'allowed' ?
          this.entity.countries.indexOf(code) > -1 :
          this.restrictedCountries.indexOf(code) > -1;

        if (code === this.entity.defaultCountry && this.countriesType === 'restricted') {
          item.disabled = true;
        }

        return item;
      });
    this.dataSource = new MatTableDataSource(items);

    if (this.countriesType === 'restricted') {
      this.allSelected = this.dataSource.filteredData
        .filter((item) => item.code !== this.entity.defaultCountry)
        .every(item => item.selected);
    }
  }

  private setDialogData() {
    this.entity = this.data.entity;
    this.countries = this.data.countries;
    this.countriesType = this.data.countriesType;
    this.restrictedCountries = this.data.restrictedCountries;
    this.allAvailableCountries = this.data.allAvailableCountries;
  }

  private setCountriesHash() {
    if (this.data.buildHash) {
      this.countriesHash = this.countries.reduce(codeArrayToObjectReducer, {});
    }
  }
}
